"use client";

import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { <PERSON>er } from "@toss/utils";
import { PointHistoryItem } from "@workspace/db/crud/common/points";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { DataTable } from "@workspace/ui/components/data-table";
import { format } from "date-fns";
import { useMemo } from "react";

const getColumnDefs = () => {
  const columnDefs: ColumnDef<PointHistoryItem>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "rowNumber",
      header: "No",
      cell: ({ getValue }) => {
        const value = getValue() as number;
        return value;
      },
    },
    {
      id: "regionCode",
      accessorKey: "srvcCd",
      header: "지역코드",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "-";
      },
    },
    {
      id: "serviceCode",
      accessorKey: "srvcCd",
      header: "서비스코드",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "-";
      },
    },
    {
      id: "serviceType",
      accessorKey: "pntTypeCd",
      header: "서비스유형",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "-";
      },
    },
    {
      id: "serviceName",
      accessorKey: "srvcCd",
      header: "서비스명",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value === "kona" ? "코나아이" : value || "-";
      },
    },
    {
      accessorKey: "pntTypeCd",
      header: "포인트유형",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const typeMap: { [key: string]: string } = {
          common: "공통",
          local: "로컬",
          event: "이벤트",
          other: "기타포인트",
        };
        return typeMap[value] || value || "-";
      },
    },
    {
      accessorKey: "pntTxType",
      header: "상세유형",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "-";
      },
    },
    {
      accessorKey: "userNm",
      header: "사용자명",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return Masker.maskName(value);
      },
    },
    {
      id: "userDid",
      accessorKey: "userDid",
      header: "DID",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value.slice(-8);
      },
    },
    {
      id: "phoneNumber",
      accessorKey: "telnoEnc",
      header: "연락처",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return Masker.maskPhoneNumber(value);
      },
    },
    {
      accessorKey: "earnAmt",
      header: "적립액",
      cell: ({ getValue }) => {
        const value = getValue() as number;
        return value.toLocaleString() + "P";
      },
    },
    {
      accessorKey: "remainAmt",
      header: "사용자잔액",
      cell: ({ getValue }) => {
        const value = getValue() as number;
        return value.toLocaleString() + "P";
      },
    },
    {
      accessorKey: "earnDate",
      header: "적립일시",
      cell: ({ getValue }) => {
        const value = getValue() as Date;
        return format(value, "yyyy.MM.dd HH:mm");
      },
    },
    {
      accessorKey: "expDate",
      header: "유효기간",
      cell: ({ getValue }) => {
        const value = getValue() as Date;
        return format(value, "yyyy.MM.dd HH:mm");
      },
    },
    {
      accessorKey: "txRslt",
      header: "성공실패",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "성공";
      },
    },
  ];

  return columnDefs;
};

export const PointTable = ({ data }: { data: PointHistoryItem[] }) => {
  const columns: ColumnDef<PointHistoryItem>[] = useMemo(
    () => getColumnDefs(),
    [],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border">
      <DataTable table={table} />
    </div>
  );
};
